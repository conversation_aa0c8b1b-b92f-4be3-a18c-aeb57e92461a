import React from "react"
import { Grid, Column } from "@module/apricot/components/grid"
import { LocationCard } from "@module/location/ui"
import { MentorCard } from "@module/mentor"
import { CenterAlignedCards } from "@module/style/components.css/center-aligned-cards"

interface ServerRenderedLocationsListProps {
  initialData?: {
    allLocations?: any[]
    allMentors?: any[]
  }
  availableCourses?: any[]
}

/**
 * Server-rendered component that displays locations immediately in DOM
 * Works without JavaScript for SEO and performance
 */
export default function ServerRenderedLocationsList({ 
  initialData, 
  availableCourses = [] 
}: ServerRenderedLocationsListProps) {
  if (!initialData) return null

  const { allLocations = [], allMentors = [] } = initialData
  const combinedData = [...allLocations, ...allMentors]

  if (combinedData.length === 0) {
    return (
      <Grid>
        <Column>
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <p>No locations or mentors found.</p>
          </div>
        </Column>
      </Grid>
    )
  }

  return (
    <Grid>
      <Column>
        <CenterAlignedCards css={{ $$xlColumn: 4, "> div": { height: "auto" } }}>
          {allLocations.map((location: any, index: number) => (
            <div key={`location-${location.id}-${index}`}>
              <LocationCard {...location} availableCourses={availableCourses} />
            </div>
          ))}
          {allMentors.map((mentor: any, index: number) => (
            <div key={`mentor-${mentor.id}-${index}`}>
              <MentorCard {...mentor} />
            </div>
          ))}
        </CenterAlignedCards>
      </Column>
    </Grid>
  )
}
