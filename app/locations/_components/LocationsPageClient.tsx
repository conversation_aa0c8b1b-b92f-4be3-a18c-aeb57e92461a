"use client"

import React from "react"
import InteractiveMap from "@module/interactive-map"
import { GlobalNavigation } from "@module/global-navigation"
import { Footer } from "@module/footer"
import { Seo } from "@module/seo/ui"
import { EnquireFormProvider } from "@module/enquire-form/service/EnquireFormContext"
import FlexibleContent from "@module/flexible-content"
import { LocationsLandingEntry } from "@module/interactive-map/style/index.css"
import { safeJsonParse } from "@module/utilities"
import type { LocationsPageClientProps } from "../types"

export default function LocationsPageClient(props: LocationsPageClientProps) {
  const { introductionBanner, flexibleContent, seo, ...interactiveMapProps } = props.data

  const hasBuiltInData = interactiveMapProps.allLocations && interactiveMapProps.allMentors && !props.currentPage

  // Parse initial data for server-rendered listings
  console.log("Debug - props.initialLocations type:", typeof props.initialLocations)
  console.log("Debug - props.initialLocations:", props.initialLocations)

  let initialData = null
  try {
    if (typeof props.initialLocations === 'string') {
      initialData = JSON.parse(props.initialLocations)
    } else {
      initialData = props.initialLocations
    }
  } catch (error) {
    console.error("Failed to parse initialLocations:", error)
  }

  console.log("Debug - parsed initialData:", initialData)

  const noJSHTML = initialData ? generateNoJSHTML(initialData, props) : `
    <div class="no-js-fallback">
      <div class="no-js-header">
        <h1>Find a Location Near You</h1>
        <p>JavaScript is required for the interactive map and filtering features.</p>
        <p>No initial data available.</p>
      </div>
    </div>
  `

  return (
    <>
      {/* Fallback for when JavaScript is completely disabled */}
      <noscript>
        <style>{noJSStyles}</style>
        <div dangerouslySetInnerHTML={{ __html: noJSHTML }} />
      </noscript>
      <EnquireFormProvider properties={props.enquireFormProperties}>
        <Seo {...seo} schema={props.schema} />
        <GlobalNavigation {...props.navigation} />
        <LocationsLandingEntry>
          <FlexibleContent data={introductionBanner} />

          {/* Debug info */}
          <div style={{ padding: '1rem', background: '#f0f0f0', margin: '1rem 0' }}>
            <p>Debug: hasBuiltInData = {String(hasBuiltInData)}</p>
            <p>Debug: initialData exists = {String(!!initialData)}</p>
            <p>Debug: locations count = {initialData?.allLocations?.length || 0}</p>
          </div>

          {hasBuiltInData ? (
            // Use data from getLocationLandingData()
            <InteractiveMap
              {...interactiveMapProps}
              enableUrlSync={true}
              initialSearchParams={props.initialSearchParams}
              currentPage={props.currentPage}
              totalPages={props.totalPages}
              totalLocations={props.totalLocations}
            />
          ) : (
            // App Router style - use separately fetched data
            <InteractiveMap
              {...interactiveMapProps}
              allLocations={props.locations || []}
              allMentors={props.mentors || []}
              initialLocations={props.initialLocations || ""}
              currentPage={props.currentPage}
              totalPages={props.totalPages}
              totalLocations={props.totalLocations}
              initialSearchParams={props.initialSearchParams}
              enableUrlSync={true}
            />
          )}
        </LocationsLandingEntry>
        <FlexibleContent data={flexibleContent} />
        <Footer {...props.footer} reversed siteName={props.siteName} />
      </EnquireFormProvider>
    </>
  )
}

const noJSStyles = `
  .no-js-fallback {
    font-family: 'Open Sans', sans-serif;
    line-height: 1.6;
    color: #333;
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
  }
  .no-js-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #EA7125;
  }
  .no-js-grid {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
  .no-js-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 1.5rem;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }
  .no-js-card h3 {
    margin: 0 0 0.5rem 0;
    color: #EA7125;
    font-size: 1.2rem;
  }
  .no-js-card p {
    margin: 0 0 0.5rem 0;
    font-size: 0.9rem;
    color: #666;
  }
  .no-js-courses {
    margin-top: 1rem;
  }
  .no-js-courses strong {
    display: block;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    color: #333;
  }
  .no-js-pill {
    display: inline-block;
    margin: 0.125rem;
    padding: 0.25rem 0.5rem;
    background: #f0f0f0;
    border-radius: 4px;
    font-size: 0.8rem;
    border: 1px solid #ddd;
  }
  .no-js-pagination {
    margin-top: 2rem;
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid #ddd;
  }
  .no-js-pagination a {
    color: #EA7125;
    text-decoration: none;
    margin: 0 0.5rem;
    padding: 0.5rem 1rem;
    border: 1px solid #EA7125;
    border-radius: 4px;
  }
  .no-js-pagination a:hover {
    background: #EA7125;
    color: white;
  }
  .no-js-notice {
    background: #fff8f3;
    border: 1px solid #EA7125;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 2rem;
    text-align: center;
  }
`

function generateNoJSHTML(initialData: any, props: LocationsPageClientProps): string {
  const escapeHtml = (text: string) =>
    text
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#039;")

  const locationsHTML =
    initialData?.allLocations
      ?.map((location: any) => {
        const coursesHTML =
          location.coursesOffered && location.coursesOffered.length > 0
            ? `<div class="no-js-courses">
          <strong>Courses Available:</strong>
          <div>
            ${location.coursesOffered
              .map((courseId: string) => {
                const course = props.data.filterByCourses.find((c: any) => c.id === courseId)
                return course ? `<span class="no-js-pill">${escapeHtml(course.title)}</span>` : ""
              })
              .join("")}
          </div>
        </div>`
            : ""

        return `<div class="no-js-card">
      <h3>${escapeHtml(location.title || "Location")}</h3>
      ${location.address ? `<p>${escapeHtml(location.address)}</p>` : ""}
      ${coursesHTML}
    </div>`
      })
      .join("") || ""

  const mentorsHTML =
    initialData?.allMentors
      ?.map((mentor: any) => {
        const qualificationsHTML =
          mentor.qualifications && mentor.qualifications.length > 0
            ? `<div class="no-js-courses">
          <strong>Qualifications:</strong>
          <div>
            ${mentor.qualifications
              .map((qual: any) => `<span class="no-js-pill">${escapeHtml(qual.title)}</span>`)
              .join("")}
          </div>
        </div>`
            : ""

        return `<div class="no-js-card">
      <h3>${escapeHtml(mentor.name || "AIPT Mentor")}</h3>
      <p>AIPT Mentor - ${escapeHtml(mentor.jobPosition || "Personal Trainer")}</p>
      ${mentor.gymName ? `<p>${escapeHtml(mentor.gymName)}</p>` : ""}
      ${qualificationsHTML}
    </div>`
      })
      .join("") || ""

  const paginationHTML =
    props.totalPages && props.totalPages > 1
      ? `<div class="no-js-pagination">
        <p>Page ${props.currentPage || 1} of ${props.totalPages}</p>
        <div>
          ${
            props.currentPage && props.currentPage > 1
              ? `<a href="/locations?page=${props.currentPage - 1}">← Previous</a>`
              : ""
          }
          ${
            props.currentPage && props.totalPages && props.currentPage < props.totalPages
              ? `<a href="/locations?page=${props.currentPage + 1}">Next →</a>`
              : ""
          }
        </div>
      </div>`
      : ""

  return `<div class="no-js-fallback">
    <div class="no-js-header">
      <h1>Find a Location Near You</h1>
      <div class="no-js-notice">
        <p><strong>JavaScript is required for the interactive map and filtering features.</strong></p>
        <p>Below is a list of all available locations:</p>
      </div>
    </div>
    <div class="no-js-grid">
      ${locationsHTML}
      ${mentorsHTML}
    </div>
    ${paginationHTML}
  </div>`
}

function generateFallbackHTML(props: LocationsPageClientProps): string {
  return `<div class="no-js-fallback">
    <div class="no-js-header">
      <h1>Find a Location Near You</h1>
      <div class="no-js-notice">
        <p><strong>JavaScript is required for the interactive map and filtering features.</strong></p>
        <p>Loading locations...</p>
      </div>
    </div>
  </div>`
}

