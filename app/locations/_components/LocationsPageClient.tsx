"use client"

import React from "react"
import InteractiveMap from "@module/interactive-map"
import { GlobalNavigation } from "@module/global-navigation"
import { Footer } from "@module/footer"
import { Seo } from "@module/seo/ui"
import { EnquireFormProvider } from "@module/enquire-form/service/EnquireFormContext"
import FlexibleContent from "@module/flexible-content"
import { LocationsLandingEntry } from "@module/interactive-map/style/index.css"
import { safeJsonParse } from "@module/utilities"
import type { LocationsPageClientProps } from "../types"

export default function LocationsPageClient(props: LocationsPageClientProps) {
  const { introductionBanner, flexibleContent, seo, ...interactiveMapProps } = props.data

  const hasBuiltInData = interactiveMapProps.allLocations && interactiveMapProps.allMentors && !props.currentPage

  // Parse initial data for server-rendered listings
  const initialData = safeJsonParse(props.initialLocations)

  return (
    <>
      <EnquireFormProvider properties={props.enquireFormProperties}>
        <Seo {...seo} schema={props.schema} />
        <GlobalNavigation {...props.navigation} />
        <LocationsLandingEntry>
          <FlexibleContent data={introductionBanner} />
          {hasBuiltInData ? (
            // Use data from getLocationLandingData()
            <InteractiveMap
              {...interactiveMapProps}
              enableUrlSync={true}
              initialSearchParams={props.initialSearchParams}
              currentPage={props.currentPage}
              totalPages={props.totalPages}
              totalLocations={props.totalLocations}
            />
          ) : (
            // App Router style - server-rendered listings with client-only map
            <InteractiveMap
              {...interactiveMapProps}
              allLocations={props.locations || []}
              allMentors={props.mentors || []}
              initialLocations={initialData}
              currentPage={props.currentPage}
              totalPages={props.totalPages}
              totalLocations={props.totalLocations}
              initialSearchParams={props.initialSearchParams}
              enableUrlSync={true}
            />
          )}
        </LocationsLandingEntry>
        <FlexibleContent data={flexibleContent} />
        <Footer {...props.footer} reversed siteName={props.siteName} />
      </EnquireFormProvider>
    </>
  )
}


