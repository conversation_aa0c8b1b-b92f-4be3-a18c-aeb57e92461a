"use client"

import React, { memo } from "react"
import { Grid } from "@module/apricot/components/grid"

import { useViewMode } from "@module/interactive-map/service/useViewMode"
import { useHasSearchBeenPerformed } from "@module/interactive-map/service/useHasSearchBeenPerformed"

import { ListPagination } from "./ListPagination"
import { SecondaryFilter } from "../../secondary-filter/ui"
import { ViewWrapper } from "../../view-wrapper/ui"
import { PostSearchView } from "./PostSearchView"
import { ServerRenderedListings } from "./ServerRenderedListings"

import { flushedGrid } from "../style/index.css"
import type { QueryProperties } from "@module/interactive-map/controller/api/listViewApi/types"

interface HybridListViewProps {
  initialData?: QueryProperties
  availableCourses?: Array<{ id: string; name: string }>
}

/**
 * Hybrid list view that shows server-rendered listings immediately
 * and switches to client-side search results when filters are applied.
 * This ensures fast initial load while maintaining interactive functionality.
 */
export const HybridListView = memo(({ initialData, availableCourses }: HybridListViewProps) => {
  const { isListView } = useViewMode()
  const hasSearched = useHasSearchBeenPerformed()

  return (
    <ViewWrapper isListView={isListView}>
      <SecondaryFilter />
      {hasSearched ? (
        // Show client-side filtered results when user has performed a search
        <PostSearchView />
      ) : (
        // Show server-rendered initial data immediately
        <Grid className={flushedGrid}>
          {initialData && (
            <ServerRenderedListings
              allLocations={initialData.allLocations || []}
              allMentors={initialData.allMentors || []}
              availableCourses={availableCourses}
            />
          )}
        </Grid>
      )}
      <ListPagination />
    </ViewWrapper>
  )
})

HybridListView.displayName = "HybridListView"
