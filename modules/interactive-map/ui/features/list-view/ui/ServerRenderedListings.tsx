import React from "react"
import { Column } from "@module/apricot/components/grid"
import { sortBy } from "lodash-es"

import { LocationCard } from "@module/location/ui"
import { MentorCard } from "@module/mentor"
import { CenterAlignedCards } from "@module/style/components.css/center-aligned-cards"
import { collect } from "@module/utilities"

import type { QueryProperties } from "@module/interactive-map/controller/api/listViewApi/types"

type ProxyLocation = QueryProperties["allLocations"][0]
type ProxyMentor = QueryProperties["allMentors"][0]
type EitherOr = ProxyLocation | ProxyMentor

interface ServerRenderedListingsProps {
  allLocations: ProxyLocation[]
  allMentors: ProxyMentor[]
  availableCourses?: Array<{ id: string; name: string }>
}

const recordIsMentorCard = (value: EitherOr): value is ProxyMentor => {
  return "qualifications" in value
}

const extractLocation = (value: EitherOr): LatLng | null => {
  if ("location" in value && value.location) {
    return value.location
  }
  return null
}

/**
 * Server-rendered listings component that displays location and mentor cards
 * without any client-side dependencies. This ensures immediate visibility
 * of content in the DOM for better performance and SEO.
 */
export const ServerRenderedListings = ({ 
  allLocations = [], 
  allMentors = [], 
  availableCourses = [] 
}: ServerRenderedListingsProps) => {
  // Combine and sort data server-side
  const combinedListingData = React.useMemo(() => {
    const combinedData = [...allLocations, ...allMentors]
    
    // Sort by distance if location data is available, otherwise maintain original order
    return sortBy(combinedData, (item) => {
      const location = extractLocation(item)
      // For server rendering, we don't have user location context
      // so we maintain the original order from the API
      return 0
    })
  }, [allLocations, allMentors])

  const toColumn = (value: EitherOr, index: number) => {
    const location = extractLocation(value)
    
    if (recordIsMentorCard(value)) {
      return (
        <div key={`mentor-${value.id}-${index}`}>
          <MentorCard {...value} />
        </div>
      )
    } else {
      return (
        <div key={`location-${value.id}-${index}`}>
          <LocationCard {...value} availableCourses={availableCourses} />
        </div>
      )
    }
  }

  if (combinedListingData.length === 0) {
    return (
      <Column>
        <div style={{ textAlign: 'center', padding: '2rem' }}>
          <p>No locations or mentors found.</p>
        </div>
      </Column>
    )
  }

  return (
    <Column>
      <CenterAlignedCards css={{ $$xlColumn: 4, "> div": { height: "auto" } }}>
        {combinedListingData.map((item, index) => toColumn(item, index))}
      </CenterAlignedCards>
    </Column>
  )
}
