"use client"

import React, { useEffect, memo, useMemo } from "react"
import { Grid } from "@module/apricot/components/grid"

import { useGetLocationsQuery } from "@module/interactive-map/controller/api/listViewApi"
import { useMapDispatch, useMapSelector } from "@module/interactive-map/service/useMapState"
import { setTotalRecords } from "@module/pagination/controller/paginationSlice"

import { getLocationState } from "../common/get-location-state"
import { flushedGrid } from "../style/index.css"

interface PreSearchViewProps {
  initialData?: any
}

export const PreSearchView = memo(({ initialData }: PreSearchViewProps) => {
  const { currentPage: page, perPage } = useMapSelector((state) => state.pagination)
  const dispatch = useMapDispatch()

  // Only fetch data if no initial data is provided (fallback for legacy mode)
  const { data, isFetching, isError } = useGetLocationsQuery({ page, perPage }, { skip: !!initialData })

  const finalData = initialData || data

  // For server-rendered data, we don't show loading states
  const shouldShowLoading = !initialData && isFetching
  const shouldShowError = !initialData && isError

  const LocationState = useMemo(
    () => getLocationState({ data: finalData, isFetching: shouldShowLoading, isError: shouldShowError }),
    [finalData, shouldShowLoading, shouldShowError],
  )

  useEffect(() => {
    if (finalData) {
      const locationCount = finalData._allLocationsMeta?.count || 0
      const mentorCount = finalData._allMentorsMeta?.count || 0
      dispatch(setTotalRecords(Math.max(locationCount, mentorCount)))
    }
  }, [finalData, dispatch])

  return (
    <Grid className={flushedGrid}>
      <LocationState
        allLocations={finalData?.allLocations}
        _allLocationsMeta={finalData?._allLocationsMeta}
        allMentors={finalData?.allMentors}
        _allMentorsMeta={finalData?._allMentorsMeta}
      />
    </Grid>
  )
})

PreSearchView.displayName = "PreSearchView"
