import React from "react"
import { Grid, Column } from "@module/apricot/components/grid"
import { LocationCard } from "@module/location/ui"
import { MentorCard } from "@module/mentor"
import { CenterAlignedCards } from "@module/style/components.css/center-aligned-cards"

interface ServerListingsProps {
  initialData?: {
    allLocations?: any[]
    allMentors?: any[]
    _allLocationsMeta?: { count: number }
    _allMentorsMeta?: { count: number }
  }
  availableCourses?: Array<{ id: string; name: string }>
}

/**
 * Server-rendered listings component that displays immediately without JS
 */
export function ServerListings({ initialData, availableCourses = [] }: ServerListingsProps) {
  if (!initialData) {
    return null
  }

  const { allLocations = [], allMentors = [] } = initialData
  const combinedData = [...allLocations, ...allMentors]

  if (combinedData.length === 0) {
    return (
      <Grid>
        <Column>
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <p>No locations or mentors found.</p>
          </div>
        </Column>
      </Grid>
    )
  }

  return (
    <Grid>
      <Column>
        <CenterAlignedCards css={{ $$xlColumn: 4, "> div": { height: "auto" } }}>
          {allLocations.map((location: any, index: number) => (
            <div key={`location-${location.id}-${index}`}>
              <LocationCard {...location} availableCourses={availableCourses} />
            </div>
          ))}
          {allMentors.map((mentor: any, index: number) => (
            <div key={`mentor-${mentor.id}-${index}`}>
              <MentorCard {...mentor} />
            </div>
          ))}
        </CenterAlignedCards>
      </Column>
    </Grid>
  )
}
