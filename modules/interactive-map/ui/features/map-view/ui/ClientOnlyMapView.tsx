"use client"

import React, { memo } from "react"
import dynamic from "next/dynamic"

import { useViewMode } from "@module/interactive-map/service/useViewMode"
import { SecondaryFilter } from "../../secondary-filter/ui"
import { ViewWrapper } from "../../view-wrapper/ui"

// Dynamic import with ssr: false to ensure client-side only rendering
const Map = dynamic(() => import("../../map/ui").then(mod => ({ default: mod.Map })), {
  ssr: false,
  loading: () => (
    <div
      style={{
        height: "500px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        backgroundColor: "#f5f5f5",
        borderRadius: "8px",
      }}
    >
      Loading map...
    </div>
  ),
})

/**
 * Client-only map view component that loads the interactive map
 * functionality without blocking the server-rendered listings.
 */
export const ClientOnlyMapView = memo(() => {
  const { isMapView } = useViewMode()

  return (
    <ViewWrapper isMapView={isMapView}>
      <SecondaryFilter />
      <Map />
    </ViewWrapper>
  )
})

ClientOnlyMapView.displayName = "ClientOnlyMapView"
