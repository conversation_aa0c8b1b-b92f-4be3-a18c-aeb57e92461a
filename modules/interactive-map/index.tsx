"use client"

import React, { useMemo } from "react"
import { Provider } from "react-redux"
import { <PERSON>rid, Column } from "@module/apricot/components/grid"
import { Box } from "@module/apricot/components/box"
import { SectionWrapper } from "@module/apricot/components/section-wrapper"
import dynamic from "next/dynamic"

import useLocalStorage from "@module/storage/service/useLocalStorage"

import configureMapStore from "./controller/store"
import { initialState as initialFilterState } from "./controller/slice/filterDataSlice"
import { initialState as initialLocationState } from "./controller/slice/locationDataSlice"
import { initialState as initialMapState } from "./controller/slice/mapSlice"
import { initialState as initialSearchState } from "./controller/slice/performedSearchSlice"
import { initialState as initialPaginationState } from "@module/pagination/controller/paginationSlice"

import { FilterForm } from "./ui/features/filter-form/ui"
import { ListView } from "./ui/features/list-view/ui"

import { KEY_FILTER_DATA_STATE } from "./common/constants"

import type { InteractiveMap as IM } from "./common/types"
import type { PageDataReturn } from "@cms/location-landing/types"

import { UrlParamsSync } from "./hooks/UrlParamsSync"
import { ViewModeSync } from "./hooks/ViewModeSync"

// Map is client-only to avoid blocking server-rendered listings
const MapView = dynamic(() => import("./ui/features/map-view/ui").then(mod => ({ default: mod.MapView })), {
  ssr: false,
  loading: () => (
    <div style={{
      height: "500px",
      background: "#f5f5f5",
      borderRadius: "8px",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      color: "#666"
    }}>
      Loading map...
    </div>
  ),
})

type PageData = PageDataReturn["data"]
type InteractiveMapProperties = Omit<PageData, "introductionBanner" | "flexibleContent">

function InteractiveMapContent({
  enableUrlSync,
  initialLocations,
}: {
  enableUrlSync?: boolean
  initialLocations?: any
}) {
  // Handle both parsed objects and JSON strings
  const parsedInitialData = useMemo(() => {
    if (!initialLocations) return null
    if (typeof initialLocations === "string") {
      try {
        return JSON.parse(initialLocations)
      } catch (error) {
        console.error("Failed to parse initialLocations:", error)
        return null
      }
    }
    // Already parsed object
    return initialLocations
  }, [initialLocations])

  return (
    <SectionWrapper bg="neutral" css={{ "@max-lg": { pt: 0 } }}>
      {/* Sync view mode from localStorage after hydration */}
      <ViewModeSync />
      {/* Sync URL parameters with filter state after hydration */}
      {enableUrlSync && <UrlParamsSync />}
      <Grid>
        <Column col={12}>
          <Box css={{ "@max-lg": { display: "none" } }}>
            <FilterForm />
          </Box>
          <MapView />
          <ListView initialData={parsedInitialData} />
        </Column>
      </Grid>
    </SectionWrapper>
  )
}

export default function InteractiveMap({
  filterByCourses,
  mixedCampusPin,
  noLocationsFoundActions,
  onCampusPin,
  onCampusTooltip,
  withAiptMentorPin,
  withAiptMentorTooltip,
  allLocations,
  allMentors,
  initialLocations,
  currentPage,
  totalPages,
  totalLocations,
  initialSearchParams,
  enableUrlSync,
}: Omit<InteractiveMapProperties, "seo"> & {
  initialLocations?: any
  currentPage?: number
  totalPages?: number
  totalLocations?: number
  initialSearchParams?: {
    course: string[]
    state: IM.State | ""
    isOnCampus: boolean
    isWithAIPTMentor: boolean
    includeSurroundingArea: boolean
    location: { latitude: number; longitude: number } | null
    viewMode: IM.ViewMode
  }
  enableUrlSync?: boolean
}) {
  const { storageValue } = useLocalStorage<IM.PerfomedSearchSlice>(KEY_FILTER_DATA_STATE)

  const preloadedState = useMemo(
    () => ({
      filter: {
        ...initialFilterState,
        // URL parameters take precedence over localStorage for SSR
        ...(initialSearchParams
          ? {
              includeSurroundingArea: initialSearchParams.includeSurroundingArea,
              isOnCampus: initialSearchParams.isOnCampus,
              isWithAIPTMentor: initialSearchParams.isWithAIPTMentor,
              state: initialSearchParams.state,
              // Only override useCurrentLocation if there's actually a location in URL params
              // Otherwise, preserve the user's stored preference
              useCurrentLocation:
                initialSearchParams.location !== null
                  ? false
                  : (storageValue?.useCurrentLocation ?? initialFilterState.useCurrentLocation),
              // Note: viewMode is managed purely client-side, not from URL parameters
              course: initialSearchParams.course.length > 0 ? initialSearchParams.course[0] : "",
            }
          : storageValue
            ? {
                includeSurroundingArea:
                  storageValue.includeSurroundingArea ?? initialFilterState.includeSurroundingArea,
                isOnCampus: storageValue.isOnCampus ?? initialFilterState.isOnCampus,
                isWithAIPTMentor: storageValue.isWithAIPTMentor ?? initialFilterState.isWithAIPTMentor,
                state: storageValue.state ?? initialFilterState.state,
                useCurrentLocation: storageValue.useCurrentLocation ?? initialFilterState.useCurrentLocation,
                // Convert course array to string for filter slice (take first course if multiple)
                course:
                  storageValue?.course && Array.isArray(storageValue.course) && storageValue.course.length > 0
                    ? storageValue.course[0]
                    : initialFilterState.course,
              }
            : {}),
        availableCourses: filterByCourses,
      },
      locationData: {
        ...initialLocationState,
        locations: allLocations,
        mentors: allMentors,
      },
      mapbox: {
        ...initialMapState,
        onCampusPin: onCampusPin.url,
        withAiptMentorPin: withAiptMentorPin.url,
        mixedCampusPin: mixedCampusPin.url,
        mapInitialised: false,
      },
      content: {
        noLocationsFoundActions,
        onCampusTooltip,
        withAiptMentorTooltip,
      },
      performedSearchData: {
        ...initialSearchState,
        // Don't pre-populate performedSearchData with URL parameters or localStorage
        // This should only be set when a user actually performs a search
      },
      pagination: {
        ...initialPaginationState,
        currentPage: currentPage ? currentPage - 1 : 0, // Convert 1-indexed to 0-indexed
        totalPages: totalPages || Math.ceil((totalLocations || allLocations.length) / 12),
        totalRecords: totalLocations || allLocations.length,
      },
    }),
    [
      initialSearchParams,
      storageValue,
      filterByCourses,
      allLocations,
      allMentors,
      onCampusPin.url,
      withAiptMentorPin.url,
      mixedCampusPin.url,
      noLocationsFoundActions,
      onCampusTooltip,
      withAiptMentorTooltip,
      currentPage,
      totalPages,
      totalLocations,
    ],
  )

  const store = useMemo(() => configureMapStore(preloadedState), [preloadedState])

  return (
    <>
      <Provider store={store}>
        <InteractiveMapContent enableUrlSync={enableUrlSync} initialLocations={initialLocations} />
      </Provider>
    </>
  )
}
