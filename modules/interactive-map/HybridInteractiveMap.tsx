"use client"

import React, { useMemo } from "react"
import { Provider } from "react-redux"
import { Grid, Column } from "@module/apricot/components/grid"
import { Box } from "@module/apricot/components/box"
import { SectionWrapper } from "@module/apricot/components/section-wrapper"

import useLocalStorage from "@module/storage/service/useLocalStorage"

import configureMapStore from "./controller/store"
import { initialState as initialFilterState } from "./controller/slice/filterDataSlice"
import { initialState as initialLocationState } from "./controller/slice/locationDataSlice"
import { initialState as initialMapState } from "./controller/slice/mapSlice"
import { initialState as initialSearchState } from "./controller/slice/performedSearchSlice"
import { initialState as initialPaginationState } from "@module/pagination/controller/paginationSlice"

import { FilterForm } from "./ui/features/filter-form/ui"
import { ClientOnlyMapView } from "./ui/features/map-view/ui/ClientOnlyMapView"
import { HybridListView } from "./ui/features/list-view/ui/HybridListView"

import { KEY_FILTER_DATA_STATE } from "./common/constants"

import type { InteractiveMap as IM } from "./common/types"
import type { PageDataReturn } from "@cms/location-landing/types"
import type { QueryProperties } from "./controller/api/listViewApi/types"

import { UrlParamsSync } from "./hooks/UrlParamsSync"
import { ViewModeSync } from "./hooks/ViewModeSync"

type PageData = PageDataReturn["data"]
type InteractiveMapProperties = Omit<PageData, "introductionBanner" | "flexibleContent">

function HybridInteractiveMapContent({
  enableUrlSync,
  initialData,
  availableCourses,
}: {
  enableUrlSync?: boolean
  initialData?: QueryProperties
  availableCourses?: Array<{ id: string; name: string }>
}) {
  return (
    <SectionWrapper bg="neutral" css={{ "@max-lg": { pt: 0 } }}>
      <ViewModeSync />
      {enableUrlSync && <UrlParamsSync />}
      <Grid>
        <Column col={12}>
          <Box css={{ "@max-lg": { display: "none" } }}>
            <FilterForm />
          </Box>
          <ClientOnlyMapView />
          <HybridListView initialData={initialData} availableCourses={availableCourses} />
        </Column>
      </Grid>
    </SectionWrapper>
  )
}

export default function HybridInteractiveMap({
  filterByCourses,
  mixedCampusPin,
  noLocationsFoundActions,
  onCampusPin,
  onCampusTooltip,
  withAiptMentorPin,
  withAiptMentorTooltip,
  allLocations,
  allMentors,
  initialData,
  currentPage,
  totalPages,
  totalLocations,
  initialSearchParams,
  enableUrlSync,
}: Omit<InteractiveMapProperties, "seo"> & {
  initialData?: QueryProperties
  currentPage?: number
  totalPages?: number
  totalLocations?: number
  initialSearchParams?: {
    course: string[]
    state: IM.State | ""
    isOnCampus: boolean
    isWithAIPTMentor: boolean
    includeSurroundingArea: boolean
    location: { latitude: number; longitude: number } | null
    viewMode: IM.ViewMode
  }
  enableUrlSync?: boolean
}) {
  const { storageValue } = useLocalStorage<IM.PerfomedSearchSlice>(KEY_FILTER_DATA_STATE)

  const preloadedState = useMemo(
    () => ({
      filter: {
        ...initialFilterState,
        // URL parameters take precedence over localStorage for SSR
        ...(initialSearchParams
          ? {
              includeSurroundingArea: initialSearchParams.includeSurroundingArea,
              isOnCampus: initialSearchParams.isOnCampus,
              isWithAIPTMentor: initialSearchParams.isWithAIPTMentor,
              state: initialSearchParams.state,
              useCurrentLocation:
                initialSearchParams.location !== null
                  ? false
                  : (storageValue?.useCurrentLocation ?? initialFilterState.useCurrentLocation),
              course: initialSearchParams.course.length > 0 ? initialSearchParams.course[0] : "",
            }
          : storageValue
            ? {
                includeSurroundingArea:
                  storageValue.includeSurroundingArea ?? initialFilterState.includeSurroundingArea,
                isOnCampus: storageValue.isOnCampus ?? initialFilterState.isOnCampus,
                isWithAIPTMentor: storageValue.isWithAIPTMentor ?? initialFilterState.isWithAIPTMentor,
                state: storageValue.state ?? initialFilterState.state,
                useCurrentLocation: storageValue.useCurrentLocation ?? initialFilterState.useCurrentLocation,
                course:
                  storageValue?.course && Array.isArray(storageValue.course) && storageValue.course.length > 0
                    ? storageValue.course[0]
                    : initialFilterState.course,
              }
            : {}),
        availableCourses: filterByCourses,
      },
      locationData: {
        ...initialLocationState,
        locations: allLocations,
        mentors: allMentors,
      },
      mapbox: {
        ...initialMapState,
        onCampusPin: onCampusPin.url,
        withAiptMentorPin: withAiptMentorPin.url,
        mixedCampusPin: mixedCampusPin.url,
        mapInitialised: false,
      },
      content: {
        noLocationsFoundActions,
        onCampusTooltip,
        withAiptMentorTooltip,
      },
      performedSearchData: {
        ...initialSearchState,
      },
      pagination: {
        ...initialPaginationState,
        currentPage: currentPage ? currentPage - 1 : 0,
        totalPages: totalPages || Math.ceil((totalLocations || allLocations.length) / 12),
        totalRecords: totalLocations || allLocations.length,
      },
    }),
    [
      initialSearchParams,
      storageValue,
      filterByCourses,
      allLocations,
      allMentors,
      onCampusPin.url,
      withAiptMentorPin.url,
      mixedCampusPin.url,
      noLocationsFoundActions,
      onCampusTooltip,
      withAiptMentorTooltip,
      currentPage,
      totalPages,
      totalLocations,
    ],
  )

  const store = useMemo(() => configureMapStore(preloadedState), [preloadedState])

  return (
    <Provider store={store}>
      <HybridInteractiveMapContent 
        enableUrlSync={enableUrlSync} 
        initialData={initialData}
        availableCourses={filterByCourses}
      />
    </Provider>
  )
}
