import { gql } from "@apollo/client"

import CMS from "@cms/core"

import { MentorCardIntegration } from "@module/mentor"
import type { CachedMentorStruct } from "@module/redux/common/types"

export interface AllFetchedMentorsResponse {
  allMentors: CachedMentorStruct.MentorResponse[]
  _allMentorsMeta: {
    count: number
  }
}

export type AllFetchedMentorsProperties = CachedMentorStruct.Mentor[]

class FetchAllAvailableMentors {
  private limit: number = 100

  private get query() {
    return gql`
      ${MentorCardIntegration.fragment}

      query FetchAllAvailableMentors($skip: IntType!, $first: IntType!) {
        allMentors(
          filter: {
            _status: { eq: published },
            location: { exists: true },
            currentlyAvailable: { eq: true }
          },
          skip: $skip, 
          first: $first
        ) {
          id
          ...${MentorCardIntegration.fragmentName}
          qualifications {
            slug
          }
        }
        _allMentorsMeta(
          filter: {
            _status: { eq: published },
            location: { exists: true },
            currentlyAvailable: { eq: true }
          },
        ) {
          count
        }
      }
    `
  }

  private executeQuery(skip: number) {
    return CMS.client.query<AllFetchedMentorsResponse>({
      query: this.query,
      variables: {
        skip,
        first: this.limit,
      },
      fetchPolicy: CMS.fetchPolicy,
    })
  }

  private async handleResultsHigherThanLimit(count: number): Promise<AllFetchedMentorsResponse["allMentors"]> {
    if (count > this.limit) {
      const additionalPages = Math.floor(count / this.limit)
      const queryPromises = []
      for (let i = 1; i <= additionalPages; i++) {
        queryPromises.push(this.executeQuery(this.limit * i))
      }

      const responses = await Promise.all(queryPromises)
      return responses.flatMap((value) => value.data.allMentors)
    }

    return []
  }

  public async execute(): Promise<AllFetchedMentorsProperties> {
    const {
      data: { _allMentorsMeta, allMentors },
    } = await this.executeQuery(0)

    const additionalMentors = await this.handleResultsHigherThanLimit(_allMentorsMeta.count)

    return [...allMentors, ...additionalMentors].map(({ id, ...response }) => ({
      id,
      ...MentorCardIntegration.transformQueryData(response),
      qualifications: response.qualifications,
    }))
  }
}

export default FetchAllAvailableMentors
